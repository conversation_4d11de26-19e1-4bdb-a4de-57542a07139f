package hero.repository

import hero.model.Community
import hero.model.Post
import hero.model.User
import hero.repository.community.CommunityRepository
import hero.repository.post.PostRepository
import hero.repository.user.UserRepository
import hero.sql.cleanUpAndMigrate
import hero.sql.jooq.JooqSQL
import hero.test.logging.TestLogger
import org.postgresql.ds.PGSimpleDataSource
import org.testcontainers.containers.PostgreSQLContainer
import javax.sql.DataSource

// this is copied version from hero.test.sql
internal object TestDatabase {
    private val container = PostgreSQLContainer("postgres:15.5")

    /**
     * Run clean and migration on every test, if slow, refactor this to lazy property and implement
     * DB cleanup after every test
     */
    fun dataSource(): DataSource {
        if (!container.isRunning) {
            container.start()
        }

        val dataSource = setupDataSource()
        cleanUpAndMigrate(dataSource)

        return dataSource
    }

    private fun setupDataSource(): DataSource =
        PGSimpleDataSource().apply {
            setURL(container.jdbcUrl)
            user = container.username
            password = container.password
        }
}

abstract class RepositoryTest {
    protected val testContext = JooqSQL.context(TestDatabase.dataSource())

    private val userRepository = UserRepository(testContext)
    private val postRepository = PostRepository(testContext, TestLogger)
    private val communityRepository = CommunityRepository(testContext)

    fun createUser(user: User) {
        userRepository.save(user)
    }

    fun createCommunity(community: Community) = communityRepository.save(community)

    fun createPost(post: Post) = postRepository.save(post)
}
