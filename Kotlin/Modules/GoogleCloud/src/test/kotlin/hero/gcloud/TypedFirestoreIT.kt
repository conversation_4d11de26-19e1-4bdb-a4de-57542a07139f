package hero.gcloud

import hero.core.annotation.NoArg
import hero.exceptions.http.NotFoundException
import hero.gcloud.FirestoreTestDatabase.testCollection
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.time.Instant
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

class TypedFirestoreIT {
    @Test
    fun `should throw not found exception if entity does not exist`() {
        val testCollection = TypedCollectionReference<TestUser>(testCollection())
        assertThrows<NotFoundException> {
            testCollection["random-id"].get()
        }
    }

    @Test
    fun `should save entity and the fetch it`() {
        val testCollection = TypedCollectionReference<TestUser>(testCollection())
        val testUser = testUser()

        val underTest = testCollection[testUser.id]
        underTest.set(testUser)
        assertEquals(testUser, underTest.get())
    }

    @Test
    fun `update single root field`() {
        val testCollection = TypedCollectionReference<TestUser>(testCollection())
        val testUser = testUser(name = "very random name", role = TestRole.MODERATOR)

        val underTest = testCollection[testUser.id]

        underTest.set(testUser)
        underTest.field(root(TestUser::name)).update("new name")

        assertEquals(testUser.copy(name = "new name"), underTest.get())

        underTest.set(testUser)
        underTest.field(TestUser::role).update(TestRole.USER)

        assertEquals(testUser.copy(role = TestRole.USER), underTest.get())
    }

    @Test
    fun `update nested field`() {
        val testCollection = TypedCollectionReference<TestUser>(testCollection())
        val testUser = testUser(creator = TestCreator(tierId = "EUR05"))

        val underTest = testCollection[testUser.id]

        underTest.set(testUser)
        underTest.field(root(TestUser::creator).path(TestCreator::tierId)).update("EUR10")

        assertEquals(testUser.copy(creator = testUser.creator.copy(tierId = "EUR10")), underTest.get())
    }

    @Test
    fun `update map entry`() {
        val testCollection = TypedCollectionReference<TestUser>(testCollection())
        val testUser = testUser(attributes = mapOf("City" to "Pilsen", "Country" to "Czechia"))
        val underTest = testCollection[testUser.id]
        underTest.set(testUser)

        underTest.field(root(TestUser::attributes).entry("City")).update("Prague")

        assertEquals(
            testUser.copy(attributes = mapOf("City" to "Prague", "Country" to "Czechia")),
            underTest.get(),
        )
    }

    @Test
    fun `increment document field by a value`() {
        val testCollection = TypedCollectionReference<TestUser>(testCollection())
        val testUser = testUser(postCount = 10)
        val underTest = testCollection[testUser.id]
        underTest.set(testUser)

        underTest.field(root(TestUser::postCount)).increment(3)
        assertEquals(testUser.copy(postCount = 13), underTest.get())
    }

    @Test
    fun `increment document nullable field should set the field to the value if the field was null`() {
        val testCollection = TypedCollectionReference<TestUser>(testCollection())
        val testUser = testUser(nullablePostCount = null)
        val underTest = testCollection[testUser.id]
        underTest.set(testUser)

        underTest.field(root(TestUser::nullablePostCount)).increment(3)
        assertEquals(testUser.copy(nullablePostCount = 3), underTest.get())
    }

    @Test
    fun `check if collection contains document`() {
        val underTest = TypedCollectionReference<TestUser>(testCollection())
        val testUser = testUser()
        underTest[testUser.id].set(testUser)

        assertTrue(testUser.id in underTest)
        assertFalse("very-random-id" in underTest)
    }

    @Test
    fun `entities are correctly counted`() {
        val underTest = TypedCollectionReference<TestUser>(testCollection())
        val testUser1 = testUser(
            id = UUID.fromString("c1330359-39d5-4938-b6af-57b93754ce9a").toString(),
            postCount = 10,
            role = TestRole.MODERATOR,
        )
        val testUser2 = testUser(
            id = UUID.fromString("c1330359-39d5-4938-b6af-57b93754ce9b").toString(),
            postCount = 8,
            role = TestRole.MODERATOR,
        )
        val testUser3 = testUser(
            id = UUID.fromString("c1330359-39d5-4938-b6af-57b93754ce9c").toString(),
            postCount = 11,
            role = TestRole.USER,
        )

        underTest[testUser1.id].set(testUser1)
        underTest[testUser2.id].set(testUser2)
        underTest[testUser3.id].set(testUser3)

        assertEquals(2, underTest.where(root(TestUser::role)).isEqualTo(TestRole.MODERATOR).count())
        assertEquals(1, underTest.where(root(TestUser::role)).isEqualTo(TestRole.USER).count())
        assertEquals(3, underTest.where(root(TestUser::postCount)).isGreaterThan(0).count())
    }

    data class BaseDocument(val string: String)

    @NoArg
    data class ExtendedDocument(val string: String, val default: String = "default-value")

    @Test
    fun `should use default values for new non-nullable fields`() {
        val firestoreCollection = testCollection()
        val baseDocumentsCollection = TypedCollectionReference<BaseDocument>(firestoreCollection)
        baseDocumentsCollection["base-document"].set(BaseDocument("base-document"))

        val underTest = TypedCollectionReference<ExtendedDocument>(firestoreCollection)
        val result = underTest["base-document"].get()

        assertEquals(ExtendedDocument("base-document", "default-value"), result)

        val firestoreResult = firestoreCollection.document("base-document")
            .get()
            .get()
            .toObject(ExtendedDocument::class.java)

        // when this starts to fail, we can start using [DocumentSnapshot::toObject] instead of jackson deserialization
        // right now this deserializes our document to this, it actually inserts null into non-nullable field:
        // ExtendedDocument(string=base-document, default=null)
        // to note: if we removed @NoArg annotation and added default value for [string] field, this assertion
        // would fail since firestore deserializer creates the instance using the no-arg constructor
        assertNotEquals(ExtendedDocument("base-document", "default-value"), firestoreResult)
        assertNotNull(firestoreResult)
        assertNull(firestoreResult.default)
    }

    @Test
    fun `should remove value from an array`() {
        val underTest = TypedCollectionReference<TestUser>(testCollection())
        val testUser = testUser(categories = listOf("sport", "news", "movies"))
        underTest[testUser.id].set(testUser)

        underTest[testUser.id].field(TestUser::categories).remove("news")

        val updatedUser = underTest[testUser.id].get()
        assertEquals(listOf("sport", "movies"), updatedUser.categories)
    }

    /**
     * Just to verify that the platform clash hack works, see [remove] for more info.
     */
    @Test
    fun `should remove value from a nullable array`() {
        val underTest = TypedCollectionReference<TestUser>(testCollection())
        val testUser = testUser(nullableCategories = listOf("sport", "news", "movies"))
        underTest[testUser.id].set(testUser)

        underTest[testUser.id].field(TestUser::nullableCategories).remove("news")

        val updatedUser = underTest[testUser.id].get()
        assertEquals(listOf("sport", "movies"), updatedUser.nullableCategories)
    }

    @Test
    fun `should not remove anything since the array is null`() {
        val underTest = TypedCollectionReference<TestUser>(testCollection())
        val testUser = testUser(nullableCategories = null)
        underTest[testUser.id].set(testUser)

        underTest[testUser.id].field(TestUser::nullableCategories).remove("news")

        val updatedUser = underTest[testUser.id].get()
        assertEquals(listOf(), updatedUser.nullableCategories)
    }

    @Test
    fun `should add a value to an array`() {
        val underTest = TypedCollectionReference<TestUser>(testCollection())
        val testUser = testUser(categories = listOf("sport", "news", "movies"))
        underTest[testUser.id].set(testUser)

        underTest[testUser.id].field(TestUser::categories).union("politics")

        val updatedUser = underTest[testUser.id].get()
        assertEquals(listOf("sport", "news", "movies", "politics"), updatedUser.categories)
    }

    /**
     * Just to verify that the platform clash hack works, see [union] for more info.
     */
    @Test
    fun `should add a value to a nullable array`() {
        val underTest = TypedCollectionReference<TestUser>(testCollection())
        val testUser = testUser(nullableCategories = listOf("sport", "news", "movies"))
        underTest[testUser.id].set(testUser)

        underTest[testUser.id].field(TestUser::nullableCategories).union("politics")

        val updatedUser = underTest[testUser.id].get()
        assertEquals(listOf("sport", "news", "movies", "politics"), updatedUser.nullableCategories)
    }

    @Test
    fun `should create a new array and add the value to it if the array is null`() {
        val underTest = TypedCollectionReference<TestUser>(testCollection())
        val testUser = testUser(nullableCategories = null)
        underTest[testUser.id].set(testUser)

        underTest[testUser.id].field(TestUser::nullableCategories).union("politics")

        val updatedUser = underTest[testUser.id].get()
        assertEquals(listOf("politics"), updatedUser.nullableCategories)
    }
}

private fun testUser(
    id: String = UUID.randomUUID().toString(),
    name: String = "ada lovelace",
    postCount: Int = 8,
    attributes: Map<String, String> = mapOf("City" to "Pilsen", "Country" to "Czechia"),
    createdAt: Instant = Instant.ofEpochSecond(1684399824),
    role: TestRole = TestRole.MODERATOR,
    creator: TestCreator = TestCreator("EUR05", listOf("acc_12511")),
    categories: List<String> = listOf("sport"),
    nullableCategories: List<String>? = listOf("sport"),
    nullablePostCount: Int? = null,
) = TestUser(
    id = id,
    name = name,
    postCount = postCount,
    attributes = attributes,
    createdAt = createdAt,
    role = role,
    creator = creator,
    categories = categories,
    nullableCategories = nullableCategories,
    nullablePostCount = null,
)

private data class TestUser(
    val id: String,
    val name: String,
    val postCount: Int,
    val nullablePostCount: Int?,
    val attributes: Map<String, String>,
    val createdAt: Instant,
    val role: TestRole,
    val creator: TestCreator,
    val categories: List<String>,
    val nullableCategories: List<String>?,
)

private data class TestCreator(val tierId: String, val stripeLegacyIds: List<String> = emptyList())

private enum class TestRole {
    MODERATOR,
    USER,
}
