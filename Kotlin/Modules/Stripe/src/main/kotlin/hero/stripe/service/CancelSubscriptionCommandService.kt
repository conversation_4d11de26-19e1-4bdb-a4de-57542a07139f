package hero.stripe.service

import com.stripe.exception.InvalidRequestException
import com.stripe.model.Charge
import com.stripe.model.Invoice
import com.stripe.model.Subscription
import com.stripe.net.RequestOptions
import com.stripe.param.InvoiceRetrieveParams
import com.stripe.param.RefundCreateParams
import com.stripe.param.SubscriptionUpdateParams
import hero.baseutils.log
import hero.baseutils.minusDays
import hero.jackson.parseEnum
import hero.model.CancelledByRole
import hero.model.Currency
import hero.model.SubscriberStatus
import hero.model.topics.RefundMethod
import java.time.Instant

class CancelSubscriptionCommandService(
    private val stripeClients: StripeClients,
) {
    fun execute(command: CancelSubscriptionImmediately): Subscription {
        val subscription = stripeClients[command.currency].subscriptions().retrieve(command.subscriptionId)
        var updatedSubscription = subscription
        val patchParams = SubscriptionUpdateParams.builder()
            .setMetadata(
                mapOf(
                    "cancelledBy" to command.cancelledBy,
                    "cancelledByRole" to command.cancelledByRole.name,
                ),
            )
            .build()

        try {
            updatedSubscription = subscription.update(patchParams)
        } catch (e: InvalidRequestException) {
            // we cannot update cancelled subscriptions, no worries
        }

        if (SubscriberStatus.of(subscription.status).isActive) {
            log.info(
                "Cancelling subscription ${subscription.id} with ${command.refundMethod}.",
                subscription.logMeta,
            )
            try {
                updatedSubscription = subscription.cancel()
            } catch (e: InvalidRequestException) {
                log.warn(
                    "Could not cancel subscription ${subscription.id}, maybe already cancelled? " +
                        "Skipping: ${e.message} [${e.code}]",
                    subscription.logMeta,
                )
            }
        }
        refundSubscription(subscription, command.refundMethod)

        return updatedSubscription
    }

    fun execute(command: CancelSubscriptionAtPeriodEnd): Subscription {
        if (command.refundMethod != RefundMethod.NONE) {
            error("Refund must not be requested when cancelling at period end.")
        }
        val subscription = stripeClients[command.currency].subscriptions().retrieve(command.subscriptionId)
        log.info(
            "Setting subscription ${subscription.id} to cancel at period end.",
            subscription.logMeta,
        )
        val patchParams = SubscriptionUpdateParams.builder()
            .setCancelAtPeriodEnd(true)
            .setMetadata(
                mapOf(
                    "cancelledBy" to command.cancelledBy,
                    "cancelledByRole" to command.cancelledByRole.name,
                ),
            )
            .build()
        return subscription.update(patchParams)
    }

    private fun refundSubscription(
        subscription: Subscription,
        refund: RefundMethod,
    ) {
        // see enum descriptions
        val currency = parseEnum<Currency>(subscription.currency)
            ?: error("Currency for ${subscription.id} could not be inferred.")

        when (refund) {
            RefundMethod.NONE -> {}
            RefundMethod.REFUND -> {
                val invoice = stripeClients[subscription.currency].invoices().retrieve(subscription.latestInvoice)
                // 100 % discounts cannot be refunded
                if (invoice.paymentIntent != null) {
                    log.info(
                        "Refunding ${subscription.id} (with connected account transfer reverse), " +
                            "payment intent ${invoice.paymentIntent}.",
                        subscription.logMeta,
                    )

                    refund(currency, true, invoice.paymentIntent, subscription)
                }
            }

            RefundMethod.REFUND_IF_NOT_PAID_OUT -> {
                val invoice = stripeClients[currency].invoices().retrieve(
                    subscription.latestInvoice,
                    InvoiceRetrieveParams.builder()
                        .addAllExpand(listOf("charge", "charge.transfer.destination_payment")).build(),
                    RequestOptions.getDefault(),
                )

                refundInvoiceIfNotPaidOut(currency, subscription, invoice)
            }
        }
    }

    internal fun refundInvoiceIfNotPaidOut(
        currency: Currency,
        subscription: Subscription,
        invoice: Invoice,
    ) {
        if (!invoice.paid || invoice.charge == null) {
            // nothing to refund
            return
        }
        if (invoice.created < Instant.now().minusDays(31).epochSecond) {
            log.info(
                "Not refunding ${subscription.id} as the last invoice is issued " +
                    "at ${Instant.ofEpochSecond(invoice.created)}",
                subscription.logMeta,
            )
            return
        }

        val destinationPayment = destinationPaymentOf(invoice)

        val payoutId = destinationPayment.metadata["payoutId"]

        if (payoutId != null) {
            log.info(
                "Subscription ${subscription.id} payment was already paid out as ${destinationPayment.id}, " +
                    "not refunding.",
                subscription.logMeta,
            )
        } else {
            log.info(
                "Subscription ${subscription.id} payment was not paid out as ${destinationPayment.id}, " +
                    "refunding.",
                subscription.logMeta,
            )

            refund(currency, !invoice.chargeObject.transferObject.reversed, invoice.paymentIntent, subscription)
        }
    }

    internal fun destinationPaymentOf(invoice: Invoice): Charge =
        invoice.chargeObject.transferObject?.destinationPaymentObject
            ?: error("Cannot refund ${invoice.subscription}: charge ${invoice.charge} is missing transferObject.")

    internal fun refund(
        currency: Currency,
        reverseTransfer: Boolean,
        paymentIntentId: String,
        subscription: Subscription,
    ) {
        try {
            stripeClients[currency].refunds().create(
                RefundCreateParams.builder()
                    // reverse only if not previously reversed to prevent failures
                    .setReverseTransfer(reverseTransfer)
                    // app fees should be refunded at the same time when reversing transfers (only US)
                    .setRefundApplicationFee(reverseTransfer)
                    .setPaymentIntent(paymentIntentId).build(),
            )
        } catch (e: InvalidRequestException) {
            // we are fine if already refunded
            if (e.code == "charge_already_refunded") {
                return
            }
            log.warn(
                "Could not cancel subscription ${subscription.id}, maybe already refunded? " +
                    "Skipping: ${e.message} [${e.code}]",
                subscription.logMeta,
            )
        }
    }

    private val Subscription.logMeta: Map<String, String?>
        get() = mapOf(
            "userId" to metadata["userId"],
            "creatorId" to metadata["creatorId"],
        )
}

data class CancelSubscriptionImmediately(
    val subscriptionId: String,
    val currency: Currency,
    val cancelledBy: String,
    val cancelledByRole: CancelledByRole,
    val refundMethod: RefundMethod,
)

data class CancelSubscriptionAtPeriodEnd(
    val subscriptionId: String,
    val currency: Currency,
    val cancelledBy: String,
    val cancelledByRole: CancelledByRole,
    val refundMethod: RefundMethod,
)
