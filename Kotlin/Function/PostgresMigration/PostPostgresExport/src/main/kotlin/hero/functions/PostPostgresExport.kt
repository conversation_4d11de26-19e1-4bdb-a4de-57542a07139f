package hero.functions

import hero.baseutils.EnvironmentVariables
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.core.logging.Logger
import hero.gcloud.FirestoreRef
import hero.gcloud.TypedCollectionReference
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.model.Post
import hero.repository.post.PostRepository
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import hero.sql.jooq.Tables
import org.jooq.DSLContext

@Suppress("Unused")
class PostPostgresExport(
    lazyContext: Lazy<DSLContext> = lazy { JooqSQL.context(ConnectorConnectionPool.dataSource) },
    systemEnv: EnvironmentVariables = SystemEnv,
    private val firestore: FirestoreRef = firestore(systemEnv.cloudProject, systemEnv.isProduction),
    private val postsCollection: TypedCollectionReference<Post> = firestore.typedCollectionOf(Post),
    private val logger: Logger = log,
) : FirestoreEventSubcriber(environmentVariables = systemEnv, retryable = true) {
    private val postRepository: PostRepository = PostRepository(lazyContext)

    override fun consume(event: FirestoreEvent) {
        val postgresPost = postRepository.find { this.where(Tables.POST.ID.eq(event.documentId)) }.firstOrNull()
        val post = postsCollection[event.documentId].get()
            .let {
                if (postgresPost != null) {
                    // we don't want to update views when syncing from firestore since we do not have the data there
                    it.copy(views = postgresPost.views)
                } else {
                    it
                }
            }
        if (post.poll == null && post.pollId != null) {
            logger.fatal("Post ${post.id} has pollId but no poll")
        }
        logger.info("Exporting post ${post.id} to Postgres")
        postRepository.save(post)
        logger.info("Done exporting post ${post.id} to Postgres")
    }
}
