Kotlin/Function/Community/SubscriptionCommunityHandler/build:
  stage: build-services
  needs:
    - Kotlin/Function/Subscriber/build
    - Kotlin/Modules/SQL/build
    - Kotlin/Modules/IntegrationTesting/build
  extends:
    - .Kotlin/job-build-gradle-module
    - .Function/variables-build

.Kotlin/Function/Community/SubscriptionCommunityHandler/variables:
  variables:
    FUNCTION_NAME: "subscription-community-handler"
    CLASS_NAME: "hero.functions.SubscriptionCommunityHandler"
    TOPIC: "SubscriberStatusChanged"

Kotlin/Function/Community/SubscriptionCommunityHandler/deploy-devel:
  needs:
    - Kotlin/Function/Community/SubscriptionCommunityHandler/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-devel
    - .Kotlin/Function/Community/SubscriptionCommunityHandler/variables

Kotlin/Function/Community/SubscriptionCommunityHandler/deploy-prod:
  needs:
    - Kotlin/Function/Community/SubscriptionCommunityHandler/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-prod
    - .Kotlin/Function/Community/SubscriptionCommunityHandler/variables
