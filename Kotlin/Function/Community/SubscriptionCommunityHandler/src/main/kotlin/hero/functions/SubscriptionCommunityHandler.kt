package hero.functions

import hero.baseutils.EnvironmentVariables
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.core.logging.Logger
import hero.model.CommunityMemberStatus
import hero.model.topics.SubscriberStatusChange
import hero.model.topics.SubscriberStatusChanged
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import hero.sql.jooq.Tables
import org.jooq.DSLContext
import java.time.Instant

@Suppress("Unused")
class SubscriptionCommunityHandler(
    lazyContext: Lazy<DSLContext> = lazy { JooqSQL.context(ConnectorConnectionPool.dataSource) },
    systemEnv: EnvironmentVariables = SystemEnv,
    private val logger: Logger = log,
) : PubSubSubscriber<SubscriberStatusChanged>(systemEnv) {
    private val context: DSLContext by lazyContext

    override fun consume(payload: SubscriberStatusChanged) {
        val userId = payload.userId
        val creatorId = payload.creatorId

        val now = Instant.now()
        if (payload.statusChange == SubscriberStatusChange.UNSUBSCRIBED) {
            val communityIds = context
                .select(Tables.COMMUNITY.ID)
                .from(Tables.COMMUNITY_MEMBER)
                .join(Tables.COMMUNITY)
                .on(Tables.COMMUNITY.ID.eq(Tables.COMMUNITY_MEMBER.COMMUNITY_ID))
                .where(Tables.COMMUNITY_MEMBER.USER_ID.eq(userId))
                .and(Tables.COMMUNITY.OWNER_ID.eq(creatorId))
                .and(Tables.COMMUNITY_MEMBER.STATE.eq(CommunityMemberStatus.ACTIVE.name))
                .fetch()

            communityIds.forEach {
                val communityId = it.value1()
                logger.info("Removing $userId from community $communityId")
                val updateCount = context
                    .update(Tables.COMMUNITY_MEMBER)
                    .set(Tables.COMMUNITY_MEMBER.STATE, CommunityMemberStatus.SUBSCRIPTION_EXPIRED.name)
                    .set(Tables.COMMUNITY_MEMBER.LEFT_AT, now)
                    .where(Tables.COMMUNITY_MEMBER.COMMUNITY_ID.eq(communityId))
                    .and(Tables.COMMUNITY_MEMBER.USER_ID.eq(userId))
                    .and(Tables.COMMUNITY_MEMBER.STATE.eq(CommunityMemberStatus.ACTIVE.name))
                    .execute()

                // we update only if something has changed, otherwise we have done this before
                if (updateCount > 0) {
                    context
                        .update(Tables.COMMUNITY)
                        .set(Tables.COMMUNITY.MEMBERS_COUNT, Tables.COMMUNITY.MEMBERS_COUNT.minus(1))
                        .where(Tables.COMMUNITY.ID.eq(communityId))
                        .execute()

                    context
                        .update(Tables.USER)
                        .set(Tables.USER.JOINED_COMMUNITIES_COUNT, Tables.USER.JOINED_COMMUNITIES_COUNT.minus(1))
                        .where(Tables.USER.ID.eq(userId))
                        .execute()
                }
            }
        } else {
            val creatorsCommunities = context
                .select(Tables.COMMUNITY.ID)
                .from(Tables.COMMUNITY)
                .where(Tables.COMMUNITY.OWNER_ID.eq(creatorId))
                .and(Tables.COMMUNITY.DELETED_AT.isNull)
                .fetch()

            creatorsCommunities.forEach {
                val communityId = it.value1()
                logger.info("Adding $userId to community $communityId")

                context.insertInto(Tables.COMMUNITY_MEMBER)
                    .set(Tables.COMMUNITY_MEMBER.COMMUNITY_ID, communityId)
                    .set(Tables.COMMUNITY_MEMBER.USER_ID, userId)
                    .set(Tables.COMMUNITY_MEMBER.JOINED_AT, now)
                    .set(Tables.COMMUNITY_MEMBER.UPDATED_AT, now)
                    .set(Tables.COMMUNITY_MEMBER.STATE, CommunityMemberStatus.ACTIVE.name)
                    .execute()

                // we update only if something has changed, otherwise we have done this before
                context
                    .update(Tables.COMMUNITY)
                    .set(Tables.COMMUNITY.MEMBERS_COUNT, Tables.COMMUNITY.MEMBERS_COUNT.plus(1))
                    .where(Tables.COMMUNITY.ID.eq(communityId))
                    .execute()

                context
                    .update(Tables.USER)
                    .set(Tables.USER.JOINED_COMMUNITIES_COUNT, Tables.USER.JOINED_COMMUNITIES_COUNT.plus(1))
                    .where(Tables.USER.ID.eq(userId))
                    .execute()
            }
        }
    }
}
