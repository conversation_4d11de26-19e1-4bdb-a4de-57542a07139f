package hero.functions

import hero.model.CommunityMemberStatus
import hero.model.topics.SubscriberStatusChange
import hero.model.topics.SubscriberStatusChanged
import hero.sql.jooq.Tables
import hero.sql.jooq.Tables.COMMUNITY_MEMBER
import hero.test.IntegrationTest
import hero.test.TestEnvironmentVariables
import hero.test.logging.TestLogger
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.time.Instant

class SubscriptionCommunityHandlerIT : IntegrationTest() {
    @Test
    fun `should remove user from communities when unsubscribed`() {
        val underTest = SubscriptionCommunityHandler(
            lazyTestContext,
            TestEnvironmentVariables,
            TestLogger,
        )

        // Create users
        testHelper.createUser("filip")
        testHelper.createUser("cestmir")
        testHelper.createUser("other-creator")

        // Create subscription
        testHelper.createSubscription(userId = "filip", creatorId = "cestmir")

        // Create communities owned by cestmir
        val community1 = testHelper.createCommunity("cestmir", name = "Community 1")
        val community2 = testHelper.createCommunity("cestmir", name = "Community 2")
        val community3 = testHelper.createCommunity("other-creator", name = "Other Community")

        // Add filip as active member to cestmir's communities
        testHelper.createCommunityMember(community1.id, "filip", CommunityMemberStatus.ACTIVE)
        testHelper.createCommunityMember(community2.id, "filip", CommunityMemberStatus.ACTIVE)
        testHelper.createCommunityMember(community3.id, "filip", CommunityMemberStatus.ACTIVE)

        // Consume unsubscribe event
        underTest.consume(
            SubscriberStatusChanged(
                "filip",
                "cestmir",
                SubscriberStatusChange.UNSUBSCRIBED,
                false,
                "",
                false,
                false,
                false,
            ),
        )

        // Verify filip was removed from cestmir's communities but not from other creator's community
        val communityMembers = testContext.selectFrom(COMMUNITY_MEMBER).fetch()

        with(communityMembers.first { it.communityId == community1.id && it.userId == "filip" }) {
            assertThat(state).isEqualTo(CommunityMemberStatus.SUBSCRIPTION_EXPIRED.name)
            assertThat(leftAt).isNotNull()
        }

        with(communityMembers.first { it.communityId == community2.id && it.userId == "filip" }) {
            assertThat(state).isEqualTo(CommunityMemberStatus.SUBSCRIPTION_EXPIRED.name)
            assertThat(leftAt).isNotNull()
        }

        with(communityMembers.first { it.communityId == community3.id && it.userId == "filip" }) {
            assertThat(state).isEqualTo(CommunityMemberStatus.ACTIVE.name)
            assertThat(leftAt).isNull()
        }

        with(testContext.selectFrom(Tables.USER).where(Tables.USER.ID.eq("filip")).fetchSingle()) {
            assertThat(joinedCommunitiesCount).isEqualTo(-2)
        }
        with(testContext.selectFrom(Tables.USER).where(Tables.USER.ID.eq("cestmir")).fetchSingle()) {
            assertThat(joinedCommunitiesCount).isEqualTo(0)
        }
        with(testContext.selectFrom(Tables.COMMUNITY).where(Tables.COMMUNITY.ID.eq(community1.id)).fetchSingle()) {
            assertThat(membersCount).isEqualTo(-1)
        }
        with(testContext.selectFrom(Tables.COMMUNITY).where(Tables.COMMUNITY.ID.eq(community2.id)).fetchSingle()) {
            assertThat(membersCount).isEqualTo(-1)
        }
    }

    @Test
    fun `should add user to communities when subscribed`() {
        val underTest = SubscriptionCommunityHandler(
            lazyTestContext,
            TestEnvironmentVariables,
            TestLogger,
        )

        // Create users
        testHelper.createUser("pavel")
        testHelper.createUser("cestmir")

        // Create communities owned by cestmir
        val community1 = testHelper.createCommunity("cestmir", name = "Community 1")
        val community2 = testHelper.createCommunity("cestmir", name = "Community 2")
        val deletedCommunity = testHelper.createCommunity(
            "cestmir",
            name = "Deleted Community",
            deletedAt = Instant.now(),
        )

        // Consume subscribe event
        underTest.consume(
            SubscriberStatusChanged(
                "pavel",
                "cestmir",
                SubscriberStatusChange.SUBSCRIBED,
                false,
                "",
                false,
                false,
                false,
            ),
        )

        // Verify pavel was added to active communities but not deleted ones
        val communityMembers = testContext.selectFrom(COMMUNITY_MEMBER).fetch()
        assertThat(communityMembers).hasSize(2)

        with(communityMembers.first { it.communityId == community1.id }) {
            assertThat(userId).isEqualTo("pavel")
            assertThat(state).isEqualTo(CommunityMemberStatus.ACTIVE.name)
            assertThat(joinedAt).isNotNull()
        }

        with(communityMembers.first { it.communityId == community2.id }) {
            assertThat(userId).isEqualTo("pavel")
            assertThat(state).isEqualTo(CommunityMemberStatus.ACTIVE.name)
            assertThat(joinedAt).isNotNull()
        }

        with(testContext.selectFrom(Tables.USER).where(Tables.USER.ID.eq("pavel")).fetchSingle()) {
            assertThat(joinedCommunitiesCount).isEqualTo(2)
        }
        with(testContext.selectFrom(Tables.USER).where(Tables.USER.ID.eq("cestmir")).fetchSingle()) {
            assertThat(joinedCommunitiesCount).isEqualTo(0)
        }
        with(testContext.selectFrom(Tables.COMMUNITY).where(Tables.COMMUNITY.ID.eq(community1.id)).fetchSingle()) {
            assertThat(membersCount).isEqualTo(1)
        }

        // Verify no membership was created for deleted community
        val deletedCommunityMembers = communityMembers.filter { it.communityId == deletedCommunity.id }
        assertThat(deletedCommunityMembers).isEmpty()
    }
}
