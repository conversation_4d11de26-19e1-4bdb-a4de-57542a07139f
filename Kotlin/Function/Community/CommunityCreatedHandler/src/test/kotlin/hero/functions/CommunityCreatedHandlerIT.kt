package hero.functions

import hero.model.CommunityMemberStatus
import hero.model.topics.CommunityCreated
import hero.sql.jooq.Tables.COMMUNITY
import hero.sql.jooq.Tables.COMMUNITY_MEMBER
import hero.sql.jooq.Tables.USER
import hero.test.IntegrationTest
import hero.test.TestEnvironmentVariables
import hero.test.TestRepositories
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class CommunityCreatedHandlerIT : IntegrationTest() {
    @Test
    fun `should add all users that are subscribed to the creator to the community`() {
        val underTest = CommunityCreatedHandler(
            lazyTestContext,
            TestEnvironmentVariables,
            TestRepositories.communityRepository,
        )
        testHelper.createUser("cestmir")
        val community = testHelper.createCommunity("cestmir", membersCount = 0)
        testHelper.createUser("filip")
        testHelper.createSubscription(userId = "filip", creatorId = "cestmir")
        testHelper.createUser("petr")
        testHelper.createSubscription(userId = "petr", creatorId = "cestmir")

        // some random user that is not going to be added to the community
        testHelper.createUser("random-user")

        underTest.consume(CommunityCreated(community.id))

        val communityMembers = testContext.selectFrom(COMMUNITY_MEMBER).fetch()
        assertThat(communityMembers).hasSize(3)
        with(communityMembers.first { it.userId == "filip" }) {
            assertThat(userId).isEqualTo("filip")
            assertThat(state).isEqualTo(CommunityMemberStatus.ACTIVE.name)
            assertThat(communityId).isEqualTo(community.id)
        }
        with(communityMembers.first { it.userId == "petr" }) {
            assertThat(userId).isEqualTo("petr")
            assertThat(state).isEqualTo(CommunityMemberStatus.ACTIVE.name)
            assertThat(communityId).isEqualTo(community.id)
        }
        // owner is also added as a member
        with(communityMembers.first { it.userId == "cestmir" }) {
            assertThat(userId).isEqualTo("cestmir")
            assertThat(state).isEqualTo(CommunityMemberStatus.ACTIVE.name)
            assertThat(communityId).isEqualTo(community.id)
        }
        assertThat(testContext.selectFrom(COMMUNITY).fetchSingle().membersCount).isEqualTo(3)

        assertThat(testContext.selectFrom(USER).where(USER.ID.eq("filip")).fetchSingle().joinedCommunitiesCount)
            .isEqualTo(1)
        assertThat(testContext.selectFrom(USER).where(USER.ID.eq("petr")).fetchSingle().joinedCommunitiesCount)
            .isEqualTo(1)
        assertThat(testContext.selectFrom(USER).where(USER.ID.eq("cestmir")).fetchSingle().joinedCommunitiesCount)
            .isEqualTo(1)
        assertThat(testContext.selectFrom(USER).where(USER.ID.eq("random-user")).fetchSingle().joinedCommunitiesCount)
            .isEqualTo(0)
    }
}
