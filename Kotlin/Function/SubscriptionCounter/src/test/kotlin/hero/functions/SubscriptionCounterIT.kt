package hero.functions

import hero.gcloud.FirestoreRef
import hero.gcloud.PubSub
import hero.model.SubscriberStatus.ACTIVE
import hero.model.SubscriberStatus.CANCELLED
import hero.model.SupportCounts
import hero.model.topics.SubscriberChanged
import hero.model.topics.SubscriberStatusChanged
import hero.test.IntegrationTest
import hero.test.IntegrationTestHelper.TestCollections
import hero.test.TestEnvironmentVariables
import hero.test.gcloud.FirestoreTestDatabase
import hero.test.logging.TestLogger
import io.mockk.called
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class SubscriptionCounterIT : IntegrationTest() {
    @Test
    fun `user subscribing to a creator should update their count stats`() {
        val underTest = prepareSubscriptionCounter(pubSubMock)
        with(testHelper) {
            createUser("hung", counts = SupportCounts(supporters = 0, supporting = 0))
            createUser("jonas", counts = SupportCounts(supporters = 0, supporting = 0))
            createSubscriber(creatorId = "jonas", userId = "hung", tierId = "EUR05", status = ACTIVE)
            createSubscription(creatorId = "jonas", userId = "hung")
        }

        underTest.consume(SubscriberChanged(userId = "hung", creatorId = "jonas"))

        assertThat(TestCollections.usersCollection["hung"].get().counts)
            .isEqualTo(
                SupportCounts(
                    supporting = 1,
                    payments = 500,
                    ownedCommunities = 0,
                    joinedCommunities = 0,
                ),
            )

        assertThat(TestCollections.subscriptionStatsCollection["hung"].get().creatorIds)
            .isEqualTo(mapOf("jonas" to "EUR05"))
        assertThat(TestCollections.usersCollection["jonas"].get().counts)
            .isEqualTo(
                SupportCounts(
                    supporters = 1,
                    incomes = 500,
                    incomesClean = 450,
                    ownedCommunities = 0,
                    joinedCommunities = 0,
                ),
            )

        verify {
            pubSubMock.publish<SubscriberStatusChanged>(
                match {
                    it.userId == "hung" && it.creatorId == "jonas"
                },
            )
        }
    }

    @Test
    fun `if subscription was already processed, nothing should be modified and no event published`() {
        val underTest = prepareSubscriptionCounter(pubSubMock)
        with(testHelper) {
            createUser("hung", counts = SupportCounts(supporting = 1, payments = 500))
            createUser("jonas", counts = SupportCounts(supporters = 1, incomes = 500, incomesClean = 450))
            createSubscriber(creatorId = "jonas", userId = "hung", tierId = "EUR05", status = ACTIVE)
            createSubscription(userId = "hung", creatorId = "jonas", status = ACTIVE)
            createSubscriberStats("jonas", mapOf("hung" to "EUR05"))
            createSubscriptionStats("hung", mapOf("jonas" to "EUR05"))
        }

        underTest.consume(SubscriberChanged(userId = "hung", creatorId = "jonas"))

        assertThat(TestCollections.subscriberStatsCollection["jonas"].get().userIds)
            .isEqualTo(mapOf("hung" to "EUR05"))
        assertThat(TestCollections.usersCollection["hung"].get().counts)
            .isEqualTo(
                SupportCounts(
                    supporting = 1,
                    payments = 500,
                    ownedCommunities = 0,
                    joinedCommunities = 0,
                ),
            )

        assertThat(TestCollections.subscriptionStatsCollection["hung"].get().creatorIds)
            .isEqualTo(mapOf("jonas" to "EUR05"))
        assertThat(TestCollections.usersCollection["jonas"].get().counts)
            .isEqualTo(
                SupportCounts(
                    supporters = 1,
                    incomes = 500,
                    incomesClean = 450,
                    ownedCommunities = 0,
                    joinedCommunities = 0,
                ),
            )

        verify {
            pubSubMock wasNot called
        }
    }

    @Test
    fun `user unsubscribing from a creator should update their count stats`() {
        val underTest = prepareSubscriptionCounter(pubSubMock)
        with(testHelper) {
            createUser(
                "hung",
                counts = SupportCounts(supporting = 1, supporters = 0, payments = 500),
            )
            createUser(
                "jonas",
                counts = SupportCounts(supporting = 0, supporters = 1, incomes = 500, incomesClean = 450),
            )
            createSubscription(userId = "hung", creatorId = "jonas", status = CANCELLED)
            createSubscriber(creatorId = "jonas", userId = "hung", tierId = "EUR05", status = CANCELLED)
            createSubscriberStats("jonas", mapOf("hung" to "EUR05"))
            createSubscriptionStats("hung", mapOf("jonas" to "EUR05"))
        }

        underTest.consume(SubscriberChanged(userId = "hung", creatorId = "jonas"))

        assertThat(TestCollections.usersCollection["hung"].get().counts)
            .isEqualTo(
                SupportCounts(
                    supporting = 0,
                    payments = 0,
                    ownedCommunities = 0,
                    joinedCommunities = 0,
                ),
            )

        assertThat(TestCollections.subscriptionStatsCollection["hung"].get().creatorIds)
            .isEqualTo(emptyMap<String, String>())
        assertThat(TestCollections.usersCollection["jonas"].get().counts)
            .isEqualTo(
                SupportCounts(
                    supporters = 0,
                    incomes = 0,
                    incomesClean = 0,
                    ownedCommunities = 0,
                    joinedCommunities = 0,
                ),
            )

        verify {
            pubSubMock.publish<SubscriberStatusChanged>(
                match {
                    it.userId == "hung" && it.creatorId == "jonas"
                },
            )
        }
    }

    @Test
    fun `if unsubscription was already processed, nothing should be modified and no event published`() {
        val underTest = prepareSubscriptionCounter(pubSubMock)
        with(testHelper) {
            createUser("hung", counts = SupportCounts(supporting = 0, payments = 0))
            createUser("jonas", counts = SupportCounts(supporters = 0, incomes = 0, incomesClean = 0))
            createSubscriber(creatorId = "jonas", userId = "hung", tierId = "EUR05", status = CANCELLED)
            createSubscriberStats("jonas", mapOf())
            createSubscriptionStats("hung", mapOf())
        }

        underTest.consume(SubscriberChanged(userId = "hung", creatorId = "jonas"))

        assertThat(TestCollections.subscriberStatsCollection["jonas"].get().userIds)
            .isEqualTo(emptyMap<String, String>())
        assertThat(TestCollections.usersCollection["hung"].get().counts)
            .isEqualTo(SupportCounts(supporting = 0, payments = 0, ownedCommunities = 0, joinedCommunities = 0))

        assertThat(TestCollections.subscriptionStatsCollection["hung"].get().creatorIds)
            .isEqualTo(emptyMap<String, String>())
        assertThat(TestCollections.usersCollection["jonas"].get().counts)
            .isEqualTo(
                SupportCounts(
                    supporters = 0,
                    incomes = 0,
                    incomesClean = 0,
                    ownedCommunities = 0,
                    joinedCommunities = 0,
                ),
            )

        verify { pubSubMock wasNot called }
    }

    private fun prepareSubscriptionCounter(pubSub: PubSub) =
        SubscriptionCounter(
            pubSub = pubSub,
            firestore = FirestoreRef(FirestoreTestDatabase.testFirestore, false),
            usersCollection = TestCollections.usersCollection,
            subStatsCollection = TestCollections.subscriptionStatsCollection,
            subscribersCollection = TestCollections.subscribersCollection,
            envVariables = TestEnvironmentVariables,
            lazyContext = lazyTestContext,
            log = TestLogger,
        )
}
