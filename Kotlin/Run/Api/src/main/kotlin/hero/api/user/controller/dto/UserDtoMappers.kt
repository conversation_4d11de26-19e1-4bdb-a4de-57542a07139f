package hero.api.user.controller.dto

import hero.api.subscriber.controller.dto.toResponse
import hero.api.user.service.UserWithCategories
import hero.contract.api.dto.CategoryResponse
import hero.gcloud.imageProxy
import hero.model.Analytics
import hero.model.Creator
import hero.model.FREE_SUBSCRIBER_TIER_ID
import hero.model.ImageAsset
import hero.model.Tier
import hero.model.User
import hero.model.UserStatus
import java.time.Instant
import kotlin.math.min

fun UserWithCategories.toResponse(): UserResponse {
    val mappedCategories = categories.map {
        CategoryResponse(
            id = it.id,
            name = it.name,
            slug = it.slug,
        )
    }

    return user.toResponse(mappedCategories)
}

fun User.toResponse(categories: List<CategoryResponse>) =
    if (status == UserStatus.DELETED) {
        deletedUserResponse(id)
    } else {
        val privacyPolicyEnabled = privacyPolicyEffectiveAt?.isBefore(Instant.now()) == true
        UserResponse(
            id = id,
            name = name,
            bio = bio,
            bioHtml = bioHtml,
            bioEn = bioEn,
            bioHtmlEn = bioHtmlEn,
            path = path,
            image = image?.toResponse(),
            hasRssFeed = hasRssFeed,
            counts = UserCountsResponse(
                supporting = counts.supporting,
                supporters = min(counts.supporters, counts.supportersThreshold ?: Long.MAX_VALUE),
                supportersThreshold = counts.supportersThreshold,
                posts = counts.posts ?: 0,
                ownedCommunities = counts.ownedCommunities ?: 0,
            ),
            verified = creator.verified,
            subscribable = creator.active,
            tier = Tier.ofId(creator.tierId).toResponse(),
            categories = categories,
            privacyPolicyEnabled = privacyPolicyEnabled,
            emailPublic = creator.emailPublic ?: if (privacyPolicyEffectiveAt != null) email else null,
            analytics = analytics,
            hasGiftsAllowed = hasGiftsAllowed,
            spotify = spotifyUri?.let {
                SpotifyResponse(it, true)
            },
            profileType = creator.profileType(),
        )
    }

fun UserWithCategories.toDetailsResponse(): UserDetailsResponse {
    val mappedCategories = categories.map {
        CategoryResponse(
            id = it.id,
            name = it.name,
            slug = it.slug,
        )
    }

    return UserDetailsResponse(
        id = user.id,
        name = user.name,
        bio = user.bio,
        bioHtml = user.bioHtml,
        bioEn = user.bioEn,
        bioHtmlEn = user.bioHtmlEn,
        path = user.path,
        image = user.image?.toResponse(),
        hasRssFeed = user.hasRssFeed,
        counts = UserDetailsCountsResponse(
            supporting = user.counts.supporting,
            supporters = user.counts.supporters,
            posts = user.counts.posts ?: 0,
            invoices = user.counts.invoices ?: 0,
            incomes = user.counts.incomes ?: 0,
            incomesClean = user.counts.incomesClean ?: 0,
            payments = user.counts.payments ?: 0,
            pendingRequests = user.counts.pendingRequests ?: 0,
        ),
        verified = user.creator.verified,
        subscribable = user.creator.active,
        tier = Tier.ofId(user.creator.tierId).toResponse(),
        categories = mappedCategories,
        email = user.email,
        creator = user.creator,
        discord = user.discord.takeIf { it?.active == true }?.let {
            DiscordResponse(
                id = it.id!!,
                guildId = it.guildId,
            )
        },
        notificationSettings = user.notificationsEnabled,
        language = user.language,
        role = user.role,
        analytics = user.analytics,
        spotify = user.let {
            SpotifyResponse(it.spotifyUri, it.spotify != null)
        },
        gjirafaLivestreamMeta = user.gjirafaLivestream,
        isOfAge = user.isOfAge,
        profileType = user.creator.profileType(),
    )
}

fun ImageAsset.toResponse() =
    ImageAsset(
        id = id.imageProxy(),
        width = this.width,
        height = this.height,
        hidden = this.hidden,
    )

private fun deletedUserResponse(id: String) =
    UserResponse(
        id = id,
        name = "",
        bio = "",
        bioHtml = "",
        bioEn = "",
        bioHtmlEn = "",
        path = "",
        image = null,
        hasRssFeed = false,
        counts = UserCountsResponse(0L, 0L, 0L, 0L, 0L),
        verified = false,
        subscribable = false,
        tier = Tier.ofId("EUR05").toResponse(),
        categories = listOf(),
        isDeleted = true,
        privacyPolicyEnabled = false,
        analytics = Analytics(),
        hasGiftsAllowed = false,
        spotify = null,
        emailPublic = null,
        profileType = UserProfileType.PRIVATE,
    )

private fun Creator.profileType(): UserProfileType =
    if (tierId == FREE_SUBSCRIBER_TIER_ID) {
        UserProfileType.PRIVATE
    } else {
        UserProfileType.PUBLIC
    }
