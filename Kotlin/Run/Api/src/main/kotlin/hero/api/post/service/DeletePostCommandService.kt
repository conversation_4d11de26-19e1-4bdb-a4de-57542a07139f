package hero.api.post.service

import hero.core.logging.Logger
import hero.exceptions.http.ForbiddenException
import hero.gcloud.PubSub
import hero.gcloud.TypedCollectionReference
import hero.model.Post
import hero.model.topics.PostState
import hero.repository.post.PostRepository
import hero.sql.jooq.Tables
import org.jooq.DSLContext
import java.time.Instant
import java.util.UUID

class DeletePostCommandService(
    lazyContext: Lazy<DSLContext>,
    private val postsCollection: TypedCollectionReference<Post>,
    private val postRepository: PostRepository,
    private val pubSub: PubSub,
    private val logger: Logger,
) {
    private val context by lazyContext

    fun execute(command: DeleteCreatorPost) {
        val post = postRepository.getById(command.postId)

        if (post.state == PostState.DELETED) {
            logger.info("Post ${post.id} is already deleted, skipping.", mapOf("userId" to command.userId))
            return
        }

        val communityId = post.communityId
        if (post.userId != command.userId && communityId == null) {
            throw ForbiddenException(
                "User ${command.userId} cannot delete post ${post.id}",
                mapOf("userId" to command.userId, "postId" to post.id),
            )
        }

        if (communityId != null) {
            val ownerId = context.select(Tables.COMMUNITY.OWNER_ID)
                .from(Tables.COMMUNITY)
                .where(Tables.COMMUNITY.ID.eq(UUID.fromString(communityId)))
                .fetchSingle()
                .value1()
            if (ownerId != command.userId) {
                throw ForbiddenException(
                    "User ${command.userId} cannot delete post ${post.id} in community $communityId",
                    mapOf("userId" to command.userId, "postId" to post.id),
                )
            }
        }

        val now = Instant.now()
        postsCollection[post.id].field(Post::state).update(PostState.DELETED)
        postsCollection[post.id].field(Post::deletedAt).update(now)
        context
            .update(Tables.POST)
            .set(Tables.POST.STATE, PostState.DELETED.name)
            .set(Tables.POST.DELETED_AT, now)
            .where(Tables.POST.ID.eq(post.id)).execute()
    }

    pubSub.publish(PostStateChanged(PostStateChange.DELETED, post))
}

data class DeleteCreatorPost(val userId: String, val postId: String)
