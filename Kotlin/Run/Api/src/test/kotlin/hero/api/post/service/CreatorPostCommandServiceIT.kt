package hero.api.post.service

import hero.api.post.service.dto.PostInput
import hero.baseutils.minus
import hero.baseutils.mockNow
import hero.baseutils.plus
import hero.exceptions.http.BadRequestException
import hero.gjirafa.GjirafaLivestreamsService
import hero.gjirafa.GjirafaUploadsService
import hero.test.IntegrationTest
import hero.test.IntegrationTestHelper.TestCollections
import hero.test.TestRepositories
import hero.test.logging.TestLogger
import hero.test.time.TestClock
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.Instant
import kotlin.time.Duration.Companion.minutes
import kotlin.time.Duration.Companion.seconds

class CreatorPostCommandServiceIT : IntegrationTest(mockInstantNow = true) {
    @Nested
    inner class CreateCreatorPost {
        @Test
        fun `should create creator post`() {
            val gjirafaServiceMock = mockk<GjirafaUploadsService>()
            val gjirafaLivestreamServiceMock = mockk<GjirafaLivestreamsService>()
            val underTest = CreatorPostCommandService(
                PostService(
                    TestCollections.postsCollection,
                    TestRepositories.postRepository,
                    gjirafaServiceMock,
                    gjirafaLivestreamServiceMock,
                    pubSubMock,
                    TestClock(Instant.ofEpochSecond(1755450161)),
                ),
                TestCollections.categoriesCollection,
                TestLogger,
            )
            testHelper.createUser("cestmir")
            testHelper.createCategory(creatorId = "cestmir", id = "sports")

            val attributes = PostInput("Textik", "<p>Textik</p>", listOf(), "delta")
            val result = underTest.execute(
                CreateCreatorPost(
                    "cestmir",
                    attributes,
                    setOf("sports"),
                    isSponsored = true,
                    isAgeRestricted = true,
                    communityId = null,
                ),
            )

            assertThat(result).isEqualTo(TestCollections.postsCollection[result.id].get())
            assertThat(result.text).isEqualTo("Textik")
            assertThat(result.textHtml).isEqualTo("<p>Textik</p>")
            assertThat(result.textDelta).isEqualTo("delta")
            assertThat(result.categories).isEqualTo(listOf("sports"))
            assertThat(result.userId).isEqualTo("cestmir")
            assertThat(result.parentUserId).isEqualTo("cestmir")
            assertThat(result.isSponsored).isTrue()
            assertThat(result.isAgeRestricted).isTrue()
        }

        @Test
        fun `should create community post`() {
            val gjirafaServiceMock = mockk<GjirafaUploadsService>()
            val gjirafaLivestreamServiceMock = mockk<GjirafaLivestreamsService>()
            val underTest = CreatorPostCommandService(
                PostService(
                    TestCollections.postsCollection,
                    TestRepositories.postRepository,
                    gjirafaServiceMock,
                    gjirafaLivestreamServiceMock,
                    pubSubMock,
                    TestClock(Instant.ofEpochSecond(1755450161)),
                ),
                TestCollections.categoriesCollection,
                TestLogger,
            )
            testHelper.createUser("cestmir")
            val community = testHelper.createCommunity("cestmir")
            testHelper.createCategory(creatorId = "cestmir", id = "sports")

            val attributes = PostInput("Textik", "<p>Textik</p>", listOf(), "delta")
            val result = underTest.execute(
                CreateCreatorPost(
                    "cestmir",
                    attributes,
                    setOf("sports"),
                    isSponsored = true,
                    isAgeRestricted = true,
                    communityId = community.id,
                ),
            )

            assertThat(result).isEqualTo(TestCollections.postsCollection[result.id].get())
            assertThat(result.text).isEqualTo("Textik")
            assertThat(result.textHtml).isEqualTo("<p>Textik</p>")
            assertThat(result.textDelta).isEqualTo("delta")
            assertThat(result.categories).isEqualTo(listOf("sports"))
            assertThat(result.userId).isEqualTo("cestmir")
            assertThat(result.parentUserId).isEqualTo("cestmir")
            assertThat(result.isSponsored).isTrue()
            assertThat(result.isAgeRestricted).isTrue()
            assertThat(result.communityId).isEqualTo(community.id.toString())
        }

        @Test
        fun `publishedAt cannot be more than 5 minutes in the past`() {
            val gjirafaServiceMock = mockk<GjirafaUploadsService>()
            val gjirafaLivestreamServiceMock = mockk<GjirafaLivestreamsService>()
            val underTest = CreatorPostCommandService(
                PostService(
                    TestCollections.postsCollection,
                    TestRepositories.postRepository,
                    gjirafaServiceMock,
                    gjirafaLivestreamServiceMock,
                    pubSubMock,
                    TestClock(Instant.ofEpochSecond(1755450161)),
                ),
                TestCollections.categoriesCollection,
                TestLogger,
            )
            testHelper.createUser("cestmir")

            val attributes = PostInput("Textik", "<p>Textik</p>", listOf())
            assertThatExceptionOfType(BadRequestException::class.java).isThrownBy {
                underTest.execute(
                    CreateCreatorPost(
                        "cestmir",
                        attributes,
                        setOf(),
                        publishedAt = Instant.now() - 6.minutes,
                        isSponsored = true,
                        isAgeRestricted = true,
                        communityId = null,
                    ),
                )
            }
        }

        @Test
        fun `categories that are not defined by the creator should not be allowed`() {
            val gjirafaServiceMock = mockk<GjirafaUploadsService>()
            val gjirafaLivestreamServiceMock = mockk<GjirafaLivestreamsService>()
            val underTest = CreatorPostCommandService(
                PostService(
                    TestCollections.postsCollection,
                    TestRepositories.postRepository,
                    gjirafaServiceMock,
                    gjirafaLivestreamServiceMock,
                    pubSubMock,
                    TestClock(Instant.ofEpochSecond(1755450161)),
                ),
                TestCollections.categoriesCollection,
                TestLogger,
            )
            testHelper.createUser("cestmir")
            testHelper.createCategory(creatorId = "cestmir", id = "sports")
            testHelper.createCategory(creatorId = "cestmir", id = "movies")

            val attributes = PostInput("Textik", "<p>Textik</p>", listOf())
            assertThatExceptionOfType(BadRequestException::class.java)
                .isThrownBy {
                    underTest.execute(
                        CreateCreatorPost(
                            "cestmir",
                            attributes,
                            // gaming is not one of cestmir's categories
                            setOf("sports", "gaming"),
                            isSponsored = true,
                            isAgeRestricted = true,
                            communityId = null,
                        ),
                    )
                }.withMessage("Invalid categories [gaming], creator 'cestmir' does not have these categories")
        }
    }

    @Nested
    inner class UpdateCreatorPost {
        @Test
        fun `should update creator post`() {
            val now = Instant.now()
            mockNow(now.toString())

            val gjirafaServiceMock = mockk<GjirafaUploadsService>()
            val gjirafaLivestreamServiceMock = mockk<GjirafaLivestreamsService>()
            val underTest = CreatorPostCommandService(
                PostService(
                    TestCollections.postsCollection,
                    TestRepositories.postRepository,
                    gjirafaServiceMock,
                    gjirafaLivestreamServiceMock,
                    pubSubMock,
                    TestClock(Instant.ofEpochSecond(1755450161)),
                ),
                TestCollections.categoriesCollection,
                TestLogger,
            )
            testHelper.createUser("cestmir")
            testHelper.createCategory(creatorId = "cestmir", id = "sports")
            testHelper.createCategory(creatorId = "cestmir", id = "movies")
            val post = testHelper.createPost(
                userId = "cestmir",
                text = "old text",
                categories = listOf("sports"),
                textHtml = "<p>old text</p>",
                updatedAt = now,
                excludedFromRss = false,
            )

            val attributes = PostInput(
                "new text",
                "<p>new text</p>",
                listOf(),
                textDelta = "delta",
            )
            val result = underTest.execute(
                UpdateCreatorPost(
                    creatorId = "cestmir",
                    postId = post.id,
                    attributes = attributes,
                    categories = setOf("movies"),
                    publishedAt = null,
                    pinnedAt = now + 10.seconds,
                    isAgeRestricted = true,
                    isSponsored = true,
                    excludeFromRss = true,
                ),
            )

            assertThat(result).isEqualTo(TestCollections.postsCollection[result.id].get())

            assertThat(result).isEqualTo(
                post.copy(
                    text = "new text",
                    textHtml = "<p>new text</p>",
                    textDelta = "delta",
                    categories = listOf("movies"),
                    pinnedAt = now + 10.seconds,
                    isSponsored = true,
                    isAgeRestricted = true,
                    excludeFromRss = true,
                ),
            )
        }

        @Test
        fun `publishedAt cannot be more than 5 minutes in the past`() {
            val gjirafaServiceMock = mockk<GjirafaUploadsService>()
            val gjirafaLivestreamServiceMock = mockk<GjirafaLivestreamsService>()
            val underTest = CreatorPostCommandService(
                PostService(
                    TestCollections.postsCollection,
                    TestRepositories.postRepository,
                    gjirafaServiceMock,
                    gjirafaLivestreamServiceMock,
                    pubSubMock,
                    TestClock(Instant.ofEpochSecond(1755450161)),
                ),
                TestCollections.categoriesCollection,
                TestLogger,
            )
            testHelper.createUser("cestmir")
            val post = testHelper.createPost("cestmir")

            val attributes = PostInput("Textik", "<p>Textik</p>", listOf())
            assertThatExceptionOfType(BadRequestException::class.java).isThrownBy {
                underTest.execute(
                    UpdateCreatorPost(
                        creatorId = "cestmir",
                        postId = post.id,
                        attributes = attributes,
                        categories = setOf(),
                        publishedAt = Instant.now() - 6.minutes,
                        isAgeRestricted = true,
                        isSponsored = true,
                        excludeFromRss = true,
                    ),
                )
            }
        }

        @Test
        fun `categories that are not defined by the creator should not be allowed`() {
            val gjirafaServiceMock = mockk<GjirafaUploadsService>()
            val gjirafaLivestreamServiceMock = mockk<GjirafaLivestreamsService>()
            val underTest = CreatorPostCommandService(
                PostService(
                    TestCollections.postsCollection,
                    TestRepositories.postRepository,
                    gjirafaServiceMock,
                    gjirafaLivestreamServiceMock,
                    pubSubMock,
                    TestClock(Instant.ofEpochSecond(1755450161)),
                ),
                TestCollections.categoriesCollection,
                TestLogger,
            )
            testHelper.createUser("cestmir")
            val post = testHelper.createPost("cestmir")
            testHelper.createCategory(creatorId = "cestmir", id = "sports")
            testHelper.createCategory(creatorId = "cestmir", id = "movies")

            val attributes = PostInput("Textik", "<p>Textik</p>", listOf())
            assertThatExceptionOfType(BadRequestException::class.java)
                .isThrownBy {
                    underTest.execute(
                        UpdateCreatorPost(
                            creatorId = "cestmir",
                            postId = post.id,
                            attributes = attributes,
                            // gaming is not one of cestmir's categories
                            categories = setOf("sports", "gaming"),
                            isAgeRestricted = true,
                            isSponsored = true,
                            excludeFromRss = false,
                        ),
                    )
                }.withMessage("Invalid categories [gaming], creator 'cestmir' does not have these categories")
        }
    }
}
