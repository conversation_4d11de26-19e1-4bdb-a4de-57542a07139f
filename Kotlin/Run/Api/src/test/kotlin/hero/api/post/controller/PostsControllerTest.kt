package hero.api.post.controller

import hero.api.gjirafaLiveAsset
import hero.api.post
import hero.api.post.service.CreatorPostQueryService
import hero.api.post.service.CreatorPostWithMeta
import hero.api.post.service.GetCreatorPosts
import hero.contract.api.dto.PagedPostResponse
import hero.core.data.Page
import hero.core.data.PageRequest
import hero.jackson.fromJson
import hero.model.LiveVideoStatus.LIVE
import io.mockk.every
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import org.http4k.core.Method
import org.http4k.core.Request
import org.http4k.core.Status
import org.junit.jupiter.api.Test

class PostsControllerTest {
    @Test
    fun `should place livestreams first`() {
        val creatorPostQueryService = mockk<CreatorPostQueryService>()
        val underTest = PostsController(
            creatorPostQueryService = creatorPostQueryService,
            creatorPostCommandService = mockk(),
            commentQueryService = mockk(),
            commentCommandService = mockk(),
        )

        val post1 = post("cestmir", id = "post1")
        val post2 = post("cestmir", id = "post2")
        val livestream1 = post("cestmir", id = "livestream1", assets = listOf(gjirafaLiveAsset("live1", LIVE)))
        val post3 = post("cestmir", id = "post3")
        val post4 = post("cestmir", id = "post4")
        val livestream2 = post("cestmir", id = "livestream2", assets = listOf(gjirafaLiveAsset("live1", LIVE)))
        val content = listOf(
            CreatorPostWithMeta(post1, listOf(), null),
            CreatorPostWithMeta(post2, listOf(), null),
            CreatorPostWithMeta(livestream1, listOf(), null),
            CreatorPostWithMeta(post3, listOf(), null),
            CreatorPostWithMeta(post4, listOf(), null),
            CreatorPostWithMeta(livestream2, listOf(), null),
        )
        every { creatorPostQueryService.execute(any<GetCreatorPosts>()) } returns Page(
            content,
            PageRequest(),
            false,
        )

        val response = underTest.routeCreatorPosts(Request(Method.GET, "/v1/users/cestmir/posts"))

        assertThat(response.status).isEqualTo(Status.OK)
        val bodyResponse = response.bodyString().fromJson<PagedPostResponse>()
        val responsePostIds = bodyResponse.content.map { it.id }
        assertThat(responsePostIds).containsExactly(
            livestream1.id,
            livestream2.id,
            post1.id,
            post2.id,
            post3.id,
            post4.id,
        )
    }
}
